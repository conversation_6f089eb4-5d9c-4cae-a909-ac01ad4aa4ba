let projectsCache = null;
let projectsCacheTimestamp = 0;
const PROJECTS_CACHE_DURATION = 10000; // 10s
let projectsPreloadPromise = null;

import React, { useEffect, useState } from "react";
import user1 from "../../../assets/user1.png";
import normal from "../../../assets/Normal.svg";
import high from "../../../assets/High.svg";
import medium from "../../../assets/Medium.svg";
import cmtIcon from "../../../assets/cmt.svg";
import fileTextIcon from "../../../assets/file-text.svg";
import { getAllProjects } from "../../../api/projectManagement";
import { getProjectFiles, getProjectDocuments } from "../../../api/documents";
import { getProjectTasks } from "../../../api/taskManagement";

const preloadProjects = async () => {
  if (projectsPreloadPromise) return projectsPreloadPromise;
  projectsPreloadPromise = (async () => {
    try {
      const res = await getAllProjects();
      const data = res.data || res;
      // Lấy số lượng tài liệu và comment thật cho từng project
      const projectsWithCounts = await Promise.all(data.map(async (p) => {
        // Lấy files
        let filesCount = 0;
        try {
          const fileRes = await getProjectFiles(p._id || p.id);
          filesCount = fileRes?.data?.totalFiles ?? (Array.isArray(fileRes?.data?.files) ? fileRes.data.files.length : 0);
        } catch (e) {
          filesCount = 0;
        }
        // Lấy tasks để đếm comment
        let tasks = [];
        try {
          const taskRes = await getProjectTasks(p._id || p.id);
          tasks = taskRes.data || taskRes || [];
        } catch {}
        const commentsCount = tasks.reduce((sum, t) => sum + (Array.isArray(t.comments) ? t.comments.length : 0), 0);
        const totalTasks = tasks.length;
        const completedTasks = tasks.filter(t => t.status === 'completed').length;
        return {
          name: p.name,
          start: p.startDate ? new Date(p.startDate).toLocaleDateString("vi-VN") : "",
          end: p.endDate ? new Date(p.endDate).toLocaleDateString("vi-VN") : "",
          members: [user1],
          comments: commentsCount,
          files: filesCount,
          priority: p.priority === "high"
            ? { label: "Cao", icon: high, color: "#ef4444" }
            : p.priority === "medium"
            ? { label: "Trung bình", icon: medium, color: "#f59e42" }
            : { label: "Thấp", icon: normal, color: "#22c55e" },
          progress: totalTasks > 0 ? completedTasks / totalTasks : 0,
          completed: completedTasks,
          total: totalTasks,
        };
      }));
      projectsCache = projectsWithCounts;
      projectsCacheTimestamp = Date.now();
      return projectsWithCounts;
    } catch {
      return [];
    }
  })();
  return projectsPreloadPromise;
};

const ProjectInProgress = () => {
  const [projects, setProjects] = useState(projectsCache || []);
  const [loading, setLoading] = useState(!projectsCache);
  const [error, setError] = useState(null);

  useEffect(() => {
    let ignore = false;
    const fetchProjects = async () => {
      const now = Date.now();
      if (projectsCache && (now - projectsCacheTimestamp) < PROJECTS_CACHE_DURATION) {
        setProjects(projectsCache);
        setLoading(false);
        // Preload in background
        preloadProjects().then(newProjects => {
          if (!ignore && newProjects) {
            setProjects(newProjects);
            setLoading(false);
          }
        });
        return;
      }
      setLoading(!projectsCache);
      setError(null);
      try {
        const newProjects = await preloadProjects();
        if (!ignore && newProjects) {
          setProjects(newProjects);
          setLoading(false);
        }
      } catch (err) {
        if (!ignore) {
          setError(err.message || "Lỗi tải dự án");
          setLoading(false);
        }
      }
    };
    fetchProjects();
    return () => { ignore = true; };
  }, []);

  useEffect(() => { preloadProjects(); }, []);

  return (
    <div style={{ background: "#fff", borderRadius: 14, padding: 24, boxShadow: "0 1px 4px #f0f0f0" }}>
      <h3 style={{ fontWeight: 600, marginBottom: 18 }}>Dự án đang thực hiện</h3>
      {error ? (
        <div style={{ color: 'red' }}>{error}</div>
      ) : (
        <div style={{ display: "flex", flexDirection: "column", gap: 18 }}>
          {loading
            ? Array.from({ length: 2 }).map((_, idx) => (
                <div key={idx} style={{ background: "#fafbfc", borderRadius: 12, padding: 18, marginBottom: 0, opacity: 0.7, minHeight: 120 }}>
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
                    <div style={{ flex: 1 }}>
                      <div style={{ width: 120, height: 16, background: '#f0f0f0', borderRadius: 4, marginBottom: 6, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      <div style={{ width: 100, height: 12, background: '#f0f0f0', borderRadius: 4, marginBottom: 8, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      <div style={{ display: "flex", alignItems: "center", gap: 6, marginBottom: 6 }}>
                        <div style={{ width: 20, height: 20, background: '#f0f0f0', borderRadius: '50%', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                        <div style={{ width: 35, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                        <div style={{ width: 35, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      </div>
                    </div>
                    <div style={{ display: "flex", alignItems: "center", gap: 6 }}>
                      <div style={{ width: 16, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      <div style={{ width: 50, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    <div style={{ width: 50, height: 12, background: '#f0f0f0', borderRadius: 4, marginBottom: 6, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                      <div style={{ flex: 1, height: 6, background: "#e5e7eb", borderRadius: 6, overflow: "hidden" }}>
                        <div style={{ width: `60%`, height: "100%", background: "#f0f0f0", animation: 'pulse 1.5s ease-in-out infinite' }} />
                      </div>
                      <div style={{ width: 35, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                    <div style={{ width: 90, height: 12, background: '#f0f0f0', borderRadius: 4, marginTop: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                  </div>
                </div>
              ))
            : projects.map((p, idx) => (
                <div key={idx} style={{ background: "#fafbfc", borderRadius: 12, padding: 18, marginBottom: 0 }}>
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
                    <div>
                      <div style={{ fontWeight: 600, fontSize: 17, color: "#000" }}>{p.name}</div>
                      <div style={{ color: "#888", fontSize: 14, margin: "2px 0 8px 0" }}>{p.start} - {p.end}</div>
                      <div style={{ display: "flex", alignItems: "center", gap: 6, marginBottom: 8 }}>
                        {p.members.map((m, i) => (
                          <img key={i} src={m} alt="avatar" style={{ width: 22, height: 22, borderRadius: "50%", border: "2px solid #fff", marginLeft: i === 0 ? 0 : -8 }} />
                        ))}
                        <span style={{ display: 'flex', alignItems: 'center', fontSize: 15, marginLeft: 8, color: "#000" }}>
                          <img src={cmtIcon} alt="comments" style={{ width: 16, height: 16, marginRight: 3 }} /> {p.comments}
                        </span>
                        <span style={{ display: 'flex', alignItems: 'center', fontSize: 15, marginLeft: 8, color: "#000" }}>
                          <img src={fileTextIcon} alt="files" style={{ width: 16, height: 16, marginRight: 3 }} /> {p.files}
                        </span>
                      </div>
                    </div>
                    <div style={{ display: "flex", alignItems: "center", gap: 6 }}>
                      <img src={p.priority.icon} alt="priority" style={{ width: 18, height: 18 }} />
                      <span style={{ color: "#000", fontWeight: 500, fontSize: 15 }}>{p.priority.label}</span>
                    </div>
                  </div>
                  <div style={{ marginTop: 10 }}>
                    <div style={{ fontWeight: 500, fontSize: 15, marginBottom: 4, color: "#000" }}>Tiến độ</div>
                    <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                      <div style={{ flex: 1, height: 7, background: "#e5e7eb", borderRadius: 6, overflow: "hidden" }}>
                        <div style={{ width: `${p.progress * 100}%`, height: "100%", background: "#1677ff" }} />
                      </div>
                      <span style={{ fontWeight: 600, color: "#000" }}>{Math.round(p.progress * 100)}%</span>
                    </div>
                    <div style={{ color: "#888", fontSize: 14, marginTop: 2 }}>{p.completed}/{p.total} công việc hoàn thành</div>
                  </div>
                </div>
              ))}
        </div>
      )}
      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default ProjectInProgress;