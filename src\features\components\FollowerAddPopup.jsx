import { useState } from "react";
import "../../styles/MemberAddPopup.css";

const FollowerAddPopup = ({
  isOpen,
  onClose,
  onAddMember,
  existingMembers = [],
  users = [],
  loadingUsers = false,
  departmentId,
  disableDepartmentFilter = false,
  leaderId,
  filterByDepartment = false,
}) => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Lọc users theo các điều kiện:
  // 1. Lọc theo departmentId (nếu có và không disable và filterByDepartment = true)
  // 2. Chỉ lấy users có role 'staff' (nếu filterByDepartment = true - dành cho project creation)
  // 3. Loại trừ leader đ<PERSON> chọn
  const filteredUsers = users.filter((user) => {
    // Lọc theo department nếu cần (cho project creation)
    if (
      filterByDepartment &&
      !disableDepartmentFilter &&
      departmentId &&
      user.departmentId !== departmentId
    ) {
      return false;
    }

    // Chỉ lấy staff (cho project creation)
    if (filterByDepartment && !disableDepartmentFilter) {
      const userRole = (user.role || "").toLowerCase();
      if (userRole !== "staff") {
        return false;
      }
    }

    // Loại trừ leader đã chọn
    if (leaderId && user.id === leaderId) {
      return false;
    }

    return true;
  });

  // Debug log
  console.log(
    "FollowerAddPopup - users:",
    users.length,
    "filteredUsers:",
    filteredUsers.length,
    "disableDepartmentFilter:",
    disableDepartmentFilter
  );
  if (users.length > 0) {
    console.log("First user sample:", users[0]);
  }
  if (existingMembers.length > 0) {
    console.log("existingMembers sample:", existingMembers[0]);
  }

  // Lọc gợi ý dựa trên email đã nhập
  const handleEmailChange = (value) => {
    setEmail(value);
    setError("");
    const inputValue = value.trim().toLowerCase();
    console.log(
      "handleEmailChange - inputValue:",
      inputValue,
      "filteredUsers.length:",
      filteredUsers.length
    );
    if (inputValue.length > 0 && filteredUsers.length > 0) {
      const filteredSuggestions = filteredUsers.filter(
        (user) =>
          ((user.email && user.email.toLowerCase().includes(inputValue)) ||
            (user.name && user.name.toLowerCase().includes(inputValue))) &&
          !existingMembers.some(
            (member) =>
              member.email &&
              user.email &&
              member.email.toLowerCase() === user.email.toLowerCase()
          )
      );
      console.log("filteredSuggestions:", filteredSuggestions.length);
      setSuggestions(filteredSuggestions);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Chọn thành viên từ gợi ý
  const handleSelectSuggestion = (user) => {
    setEmail(user.email);
    setSuggestions([]);
    setShowSuggestions(false);
  };

  // Xử lý thêm thành viên
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!email.trim()) {
      setError("Vui lòng nhập email người theo dõi");
      return;
    }

    // Kiểm tra email có tồn tại trong hệ thống không
    const user = users.find(
      (u) => u.email.toLowerCase() === email.toLowerCase()
    );
    if (!user) {
      setError("Email không tồn tại trong hệ thống");
      return;
    }

    // Kiểm tra thành viên đã được thêm chưa
    if (existingMembers.some((member) => member.email === email)) {
      setError("Người này đã được thêm vào danh sách theo dõi");
      return;
    }

    // Thêm thành viên
    onAddMember(user);

    // Reset form
    setEmail("");
    setError("");
    setSuggestions([]);
    setShowSuggestions(false);
    onClose();
  };

  // Đóng popup
  const handleClose = () => {
    setEmail("");
    setError("");
    setSuggestions([]);
    setShowSuggestions(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="member-add-overlay">
      <div className="member-add-popup">
        <div className="member-add-header">
          <h3>Thêm người theo dõi</h3>
          <button className="member-add-close-btn" onClick={handleClose}>
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="member-add-form">
          <div className="member-add-form-group">
            <label>Email người theo dõi</label>
            <div className="member-add-input-container">
              <input
                type="text"
                placeholder={
                  loadingUsers
                    ? "Đang tải danh sách người dùng..."
                    : "Nhập email người theo dõi..."
                }
                value={email}
                onChange={(e) => handleEmailChange(e.target.value)}
                className={`member-add-input ${error ? "error" : ""}`}
                autoComplete="off"
                disabled={loadingUsers}
              />

              {/* Danh sách gợi ý */}
              {showSuggestions && suggestions.length > 0 && !loadingUsers && (
                <div className="member-add-suggestions">
                  {suggestions.map((user, index) => (
                    <div
                      key={index}
                      className="member-add-suggestion-item"
                      onClick={() => handleSelectSuggestion(user)}
                    >
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className="suggestion-avatar"
                      />
                      <div className="suggestion-info">
                        <div className="suggestion-name">{user.name}</div>
                        <div className="suggestion-email">{user.email}</div>
                        <div className="suggestion-department">
                          {user.department} - {user.role}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {error && <div className="member-add-error">{error}</div>}
          </div>

          <div className="member-add-actions">
            <button
              type="button"
              className="member-add-cancel-btn"
              onClick={handleClose}
            >
              Hủy
            </button>
            <button
              type="submit"
              className="member-add-submit-btn"
              disabled={loadingUsers}
            >
              Thêm người theo dõi
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FollowerAddPopup;
