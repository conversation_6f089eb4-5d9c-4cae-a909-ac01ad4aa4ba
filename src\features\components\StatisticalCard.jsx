import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import "../../styles/StatisticalCard.css";
import folderIcon from "../../assets/folder.svg";
import { STATISTICS_ENDPOINTS } from "../../api/endpoints";

const tabList = [
	{ label: "Báo cáo công việc", value: "/statistical/reports" },
	{ label: "Thống kê công việc", value: "/statistical/list" },
];

// Cache để tránh gọi API nhiều lần
let statsCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 10000; // Giảm xuống 10 giây để load nhanh hơn

// Preload data for instant loading
let preloadPromise = null;

const StatisticalCard = ({
	fileIcon,
	chartIcon,
	userGroupIcon,
	filterIcon,
	downloadIcon,
	onFilter,
	onExport,
}) => {
	const navigate = useNavigate();
	const location = useLocation();
	const [stats, setStats] = useState({
		totalProjects: 0,
		totalTasks: 0,
		completedTasks: 0,
		totalMembers: 0,
		avgProgress: 0,
	});
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);

	// Preload function
	const preloadStats = async () => {
		if (preloadPromise) return preloadPromise;
		
		preloadPromise = (async () => {
			try {
				const token = localStorage.getItem('token');
				const headers = {
					'Authorization': 'Bearer ' + token,
					'Content-Type': 'application/json',
				};

				// Chỉ gọi 2 API chính, không cần chi tiết từng project
				const [res1, res2] = await Promise.all([
					fetch(STATISTICS_ENDPOINTS.ALL_DEPARTMENTS_PROJECTS, { headers }),
					fetch(STATISTICS_ENDPOINTS.STAFF_TASK_ASSIGNMENTS, { headers })
				]);

				const data1 = await res1.json();
				const data2 = await res2.json();

				// Tổng số dự án
				let totalProjects = 0;
				if (Array.isArray(data1.data?.overallStats?.totalStats)) {
					totalProjects = data1.data.overallStats.totalStats.reduce(
						(sum, item) => sum + (item.totalProjects || 0), 0
					);
				} else if (data1.data?.totalProjects) {
					totalProjects = data1.data.totalProjects;
				}

				// Lấy số lượng công việc và công việc hoàn thành từ backend nếu có
				let totalTasks = 0, completedTasks = 0;
				if (typeof data1.data?.totalTasks === 'number') {
					totalTasks = data1.data.totalTasks;
					completedTasks = typeof data1.data?.completedTasks === 'number' ? data1.data.completedTasks : 0;
				} else if (Array.isArray(data2.data?.departmentTaskStats)) {
					totalTasks = data2.data.departmentTaskStats.reduce((sum, dept) => sum + (dept.departmentTotalTasks || 0), 0);
					completedTasks = data2.data.departmentTaskStats.reduce((sum, dept) => sum + (dept.departmentCompletedTasks || 0), 0);
				} else if (Array.isArray(data2.data?.taskDistribution?.statusDistribution)) {
					const statusArr = data2.data.taskDistribution.statusDistribution;
					completedTasks = statusArr.find(s => s._id === 'completed')?.count || 0;
					totalTasks = statusArr.reduce((sum, s) => sum + (s.count || 0), 0);
				} else if (data2.data?.totalTasks) {
					totalTasks = data2.data.totalTasks;
					completedTasks = data2.data.completedTasks || 0;
				}

				// Lấy tổng số thành viên thật từ resourceAllocation
				let totalMembers = 0;
				if (Array.isArray(data1.data?.resourceAllocation)) {
					totalMembers = data1.data.resourceAllocation.reduce((sum, dept) => sum + (dept.humanResources?.total || 0), 0);
				} else {
					totalMembers = Math.round(totalProjects * 3); // fallback nếu không có dữ liệu
				}

									// Lấy avgProgress từ backend nếu có, fallback nếu không có
					let avgProgress = 0;

					if (totalTasks > 0 && completedTasks > 0) {
						// Tính toán dựa trên số công việc hoàn thành
						avgProgress = Math.round((completedTasks / totalTasks) * 100);
					} else if (Array.isArray(data1.data?.overallStats?.totalStats) && data1.data.overallStats.totalStats.length > 0) {
						avgProgress = data1.data.overallStats.totalStats[0].avgProgress || 0;
					} else if (typeof data1.data?.avgProgress === 'number') {
						avgProgress = data1.data.avgProgress;
					} else {
						// Tính toán tiến độ trung bình dựa trên trạng thái dự án
						const projectStatuses = data1.data?.projects || [];
						if (projectStatuses.length > 0) {
							const totalProgress = projectStatuses.reduce((sum, project) => {
								const status = String(project.status || '').toLowerCase();
								if (status === 'completed' || status === 'hoàn thành') return sum + 100;
								if (status === 'in-progress' || status === 'in_progress' || status === 'đang triển khai') return sum + 50;
								if (status === 'review' || status === 'consider' || status === 'đang xem xét') return sum + 75;
								if (status === 'waiting' || status === 'pending' || status === 'đang chờ') return sum + 0;
								return sum + (project.progress || 0);
							}, 0);
							avgProgress = Math.round(totalProgress / projectStatuses.length);
						} else {
							avgProgress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
						}
					}
				// Làm tròn 2 chữ số thập phân nếu là số thực
				avgProgress = Math.round(avgProgress * 100) / 100;

				const newStats = {
					totalProjects,
					totalTasks,
					completedTasks,
					totalMembers,
					avgProgress,
				};

				// Lưu vào cache
				statsCache = newStats;
				cacheTimestamp = Date.now();
				
				return newStats;
			} catch (err) {
				console.warn('Error preloading stats:', err);
				return null;
			}
		})();
		
		return preloadPromise;
	};

	useEffect(() => {
		const fetchStats = async () => {
			const now = Date.now();
			if (statsCache && (now - cacheTimestamp) < CACHE_DURATION) {
				setStats(statsCache);
				setLoading(false);
				return;
			}

			// Nếu không có cache, mới set loading=true
			if (!statsCache) setLoading(true);
			setError(null);
			
			try {
				// Use preloaded data if available
				let newStats = null;
				if (preloadPromise) {
					newStats = await preloadPromise;
				} else {
					// Timeout để tránh loading vô hạn
					const timeoutId = setTimeout(() => {
						console.warn('API timeout, using fallback data');
						const fallbackStats = {
							totalProjects: 8,
							totalTasks: 45,
							completedTasks: 28,
							totalMembers: 24,
							avgProgress: 62,
						};
						setStats(fallbackStats);
						setError('Kết nối chậm, hiển thị dữ liệu mẫu');
						setLoading(false);
					}, 2000);
					
					const token = localStorage.getItem('token');
					const headers = {
						'Authorization': 'Bearer ' + token,
						'Content-Type': 'application/json',
					};

					// Chỉ gọi 2 API chính, không cần chi tiết từng project
					const [res1, res2] = await Promise.all([
						fetch(STATISTICS_ENDPOINTS.ALL_DEPARTMENTS_PROJECTS, { headers }),
						fetch(STATISTICS_ENDPOINTS.STAFF_TASK_ASSIGNMENTS, { headers })
					]);

					// Clear timeout nếu thành công
					clearTimeout(timeoutId);

					const data1 = await res1.json();
					const data2 = await res2.json();

					// Tổng số dự án
					let totalProjects = 0;
					if (Array.isArray(data1.data?.overallStats?.totalStats)) {
						totalProjects = data1.data.overallStats.totalStats.reduce(
							(sum, item) => sum + (item.totalProjects || 0), 0
						);
					} else if (data1.data?.totalProjects) {
						totalProjects = data1.data.totalProjects;
					}

					// Lấy số lượng công việc và công việc hoàn thành từ backend nếu có
					let totalTasks = 0, completedTasks = 0;
					if (typeof data1.data?.totalTasks === 'number') {
						totalTasks = data1.data.totalTasks;
						completedTasks = typeof data1.data?.completedTasks === 'number' ? data1.data.completedTasks : 0;
					} else if (Array.isArray(data2.data?.departmentTaskStats)) {
						totalTasks = data2.data.departmentTaskStats.reduce((sum, dept) => sum + (dept.departmentTotalTasks || 0), 0);
						completedTasks = data2.data.departmentTaskStats.reduce((sum, dept) => sum + (dept.departmentCompletedTasks || 0), 0);
					} else if (Array.isArray(data2.data?.taskDistribution?.statusDistribution)) {
						const statusArr = data2.data.taskDistribution.statusDistribution;
						completedTasks = statusArr.find(s => s._id === 'completed')?.count || 0;
						totalTasks = statusArr.reduce((sum, s) => sum + (s.count || 0), 0);
					} else if (data2.data?.totalTasks) {
						totalTasks = data2.data.totalTasks;
						completedTasks = data2.data.completedTasks || 0;
					}

					// Lấy tổng số thành viên thật từ resourceAllocation
					let totalMembers = 0;
					if (Array.isArray(data1.data?.resourceAllocation)) {
						totalMembers = data1.data.resourceAllocation.reduce((sum, dept) => sum + (dept.humanResources?.total || 0), 0);
					} else {
						totalMembers = Math.round(totalProjects * 3); // fallback nếu không có dữ liệu
					}

					// Lấy avgProgress từ backend nếu có, fallback nếu không có
					let avgProgress = 0;
					if (Array.isArray(data1.data?.overallStats?.totalStats) && data1.data.overallStats.totalStats.length > 0) {
						avgProgress = data1.data.overallStats.totalStats[0].avgProgress || 0;
					} else if (typeof data1.data?.avgProgress === 'number') {
						avgProgress = data1.data.avgProgress;
					} else {
						// Tính toán tiến độ trung bình dựa trên trạng thái dự án
						const projectStatuses = data1.data?.projects || [];
						if (projectStatuses.length > 0) {
							const totalProgress = projectStatuses.reduce((sum, project) => {
								const status = String(project.status || '').toLowerCase();
								if (status === 'completed' || status === 'hoàn thành') return sum + 100;
								if (status === 'in-progress' || status === 'in_progress' || status === 'đang triển khai') return sum + 50;
								if (status === 'review' || status === 'consider' || status === 'đang xem xét') return sum + 75;
								return sum + (project.progress || 0);
							}, 0);
							avgProgress = Math.round(totalProgress / projectStatuses.length);
						} else {
							avgProgress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
						}
					}
					// Làm tròn 2 chữ số thập phân nếu là số thực
					avgProgress = Math.round(avgProgress * 100) / 100;

					newStats = {
						totalProjects,
						totalTasks,
						completedTasks,
						totalMembers,
						avgProgress,
					};

					// Lưu vào cache
					statsCache = newStats;
					cacheTimestamp = now;
				}

				if (newStats) {
					setStats(newStats);
				} else {
					// Fallback data nếu preload fail
					const fallbackStats = {
						totalProjects: 8,
						totalTasks: 45,
						completedTasks: 28,
						totalMembers: 24,
						avgProgress: 62,
					};
					setStats(fallbackStats);
					setError('Không thể kết nối server, hiển thị dữ liệu mẫu');
				}
			} catch (err) {
				console.warn('Error fetching stats:', err);
				
				// Fallback data ngay lập tức nếu API fail
				const fallbackStats = {
					totalProjects: 8,
					totalTasks: 45,
					completedTasks: 28,
					totalMembers: 24,
					avgProgress: 62,
				};
				
				setStats(fallbackStats);
				setError('Không thể kết nối server, hiển thị dữ liệu mẫu');
			} finally {
				setLoading(false);
			}
		};

		fetchStats();
	}, [location.pathname]); // Đảm bảo chạy lại khi đổi tab

	// Start preloading when component mounts
	useEffect(() => {
		preloadStats();
	}, []);

	const handleExportExcel = () => {
		if (onExport) onExport("excel");
	};

	const handleExportPDF = () => {
		if (onExport) onExport("pdf");
	};

	if (loading) {
		return (
			<div className="statistical-cards-wrapper">
				<div className="statistical-list-title">
					<img
						src={folderIcon}
						alt="folder"
						style={{ width: 24, height: 24, marginRight: 8, marginBottom: 3, verticalAlign: 'middle' }}
					/>
					Báo cáo công v
				</div>
				<div className="statistical-cards">
					{/* Skeleton cho 4 cards */}
					{[1, 2, 3, 4].map((i) => (
						<div key={i} className="stat-card" style={{ opacity: 0.7 }}>
							<div className="stat-title stat-title-flex">
								<div style={{ 
									width: '80px', 
									height: '16px', 
									background: '#f0f0f0', 
									borderRadius: '4px',
									animation: 'pulse 1.5s ease-in-out infinite'
								}}></div>
								<div style={{ 
									width: 20, 
									height: 20, 
									background: '#f0f0f0', 
									borderRadius: '4px',
									animation: 'pulse 1.5s ease-in-out infinite'
								}}></div>
							</div>
							<div style={{ 
								width: '60px', 
								height: '32px', 
								background: '#f0f0f0', 
								borderRadius: '4px',
								marginBottom: '4px',
								animation: 'pulse 1.5s ease-in-out infinite'
							}}></div>
							<div style={{ 
								width: '100px', 
								height: '14px', 
								background: '#f0f0f0', 
								borderRadius: '4px',
								animation: 'pulse 1.5s ease-in-out infinite'
							}}></div>
						</div>
					))}
				</div>

				<div className="statistical-header-row">
					<div className="tabs-list">
						{tabList.map((tab) => (
							<button
								key={tab.value}
								className={`tabs-trigger${
									location.pathname === tab.value ? " active" : ""
								}`}
								onClick={() => navigate(tab.value)}
								type="button"
							>
								{tab.label}
							</button>
						))}
					</div>
					<div className="statistical-actions">
						<button className="btn-export" type="button" disabled>
							<img
								src={downloadIcon}
								alt="Xuất Excel"
								style={{ width: 18, marginRight: 6, opacity: 0.5 }}
							/>
							Xuất Excel
						</button>
					</div>
				</div>
				
				{/* CSS Animation */}
				<style>{`
					@keyframes pulse {
						0% { opacity: 1; }
						50% { opacity: 0.5; }
						100% { opacity: 1; }
					}
				`}</style>
			</div>
		);
	}

	if (error) {
		return (
			<div className="statistical-cards-wrapper">
				<div className="statistical-list-title">
					<img
						src={folderIcon}
						alt="folder"
						style={{ width: 22, height: 22, marginRight: 8, marginBottom: 3, verticalAlign: 'middle' }}
					/>
					Báo cáo công việc
				</div>
				<div style={{ padding: 20, textAlign: 'center', color: 'red' }}>Lỗi: {error}</div>
			</div>
		);
	}

	return (
		<div className="statistical-cards-wrapper">
			<div className="statistical-list-title">
				<img
					src={folderIcon}
					alt="folder"
					style={{ width: 22, height: 22, marginRight: 8, marginBottom: 3, verticalAlign: 'middle' }}
				/>
				Báo cáo công việc
			</div>
			<div className="statistical-cards">
				<div className="stat-card">
					<div className="stat-title stat-title-flex">
						Tổng dự án
						<img
							src={fileIcon}
							alt="Tổng dự án"
							style={{ width: 20, marginBottom: -4 }}
						/>
					</div>
					<div className="stat-value">{stats.totalProjects}</div>
					<div className="stat-desc">+2 tháng trước</div>
				</div>
				<div className="stat-card">
					<div className="stat-title stat-title-flex">
						Công việc
						<img
							src={chartIcon}
							alt="Công việc"
							style={{ width: 20, marginBottom: -4 }}
						/>
					</div>
					<div className="stat-value">{stats.totalTasks}</div>
					<div className="stat-desc">{stats.completedTasks} Hoàn thành</div>
				</div>
				<div className="stat-card">
					<div className="stat-title stat-title-flex">
						Thành viên
						<img
							src={userGroupIcon}
							alt="Thành viên"
							style={{ width: 20, marginBottom: -4 }}
						/>
					</div>
					<div className="stat-value">{stats.totalMembers}</div>
					<div className="stat-desc">+1 tháng trước</div>
				</div>
				<div className="stat-card">
					<div className="stat-title stat-title-flex">
						Tiến độ TB
						<img
							src={fileIcon}
							alt="Tiến độ TB"
							style={{ width: 20, marginBottom: -4 }}
						/>
					</div>
					<div className="stat-value">{stats.avgProgress}%</div>
					<div className="stat-desc">+6 tuần trước</div>
				</div>
			</div>

			<div className="statistical-header-row">
				<div className="tabs-list">
					{tabList.map((tab) => (
						<button
							key={tab.value}
							className={`tabs-trigger${
								location.pathname === tab.value ? " active" : ""
							}`}
							onClick={() => navigate(tab.value)}
							type="button"
						>
							{tab.label}
						</button>
					))}
				</div>
				<div className="statistical-actions">
					<button className="btn-export" type="button" onClick={handleExportExcel}>
						<img
							src={downloadIcon}
							alt="Xuất Excel"
							style={{ width: 18, marginRight: 6 }}
						/>
						Xuất Excel
					</button>
					{/* <button className="btn-export" type="button" onClick={handleExportPDF}>
						<img
							src={fileIcon}
							alt="Xuất PDF"
							style={{ width: 18, marginRight: 6 }}
						/>
						Xuất PDF
					</button> */}
				</div>
			</div>
		</div>
	);
};

export default StatisticalCard;