import React, { useState, useRef, useEffect } from 'react';
import '../../styles/CreateNote.css';
import closeLoc from '../../assets/closeLoc.svg';
import loadFileIcon from "../../assets/loadfile.svg";
import fileTextIcon from "../../assets/file-text.svg";
import { validateNoteForm } from '../../utils/validation';
import { showToast } from '../../utils/toastUtils';

const CreateNote = ({ onClose, onCreate }) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [errors, setErrors] = useState({ title: '', content: '' });
  const modalRef = useRef(null);

  const validate = () => {
    const validationErrors = validateNoteForm({ title, content });
    setErrors(validationErrors);
    return !validationErrors.title && !validationErrors.content;
  };

   const handleAddFile = () => {
    fileInputRef.current?.click();
  };

   const handleFileUpload = (e) => {
  const files = Array.from(e.target.files);
  setAttachments(prev => [...prev, ...files]);
  e.target.value = "";
};

  const [formData, setFormData] = useState({
  name: "",
  description: "",
  startDate: "",
  endDate: "",
  priority: "medium",
  members: [],
});
const [attachments, setAttachments] = useState([]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    let createdNoteId = null;
    let createdNoteRaw = null;
    if (onCreate) {
      const result = await onCreate({ title, content });
      if (result) {
        if (result.id) createdNoteId = result.id;
        else if (result._id) createdNoteId = result._id;
        else if (result.data && result.data._id) createdNoteId = result.data._id;
        createdNoteRaw = result;
      }
    }
    let updatedNote = createdNoteRaw;
    // Upload file nếu có file đính kèm và đã tạo ghi chú thành công
    if (attachments.length > 0 && createdNoteId) {
  const form = new FormData();
  attachments.forEach(file => form.append('files', file));
      const token = localStorage.getItem('token');
      try {
        const res = await fetch(
          require('../../api/endpoints').PERSONAL_NOTES_ENDPOINTS.UPLOAD_FILES(createdNoteId),
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
            },
            body: form,
          }
        );
        let uploadResult = null;
        try {
          uploadResult = await res.json();
          // eslint-disable-next-line no-console
          console.log('Kết quả upload file:', uploadResult);
        } catch {}
        if (res.ok) {
          showToast('Tải tệp thành công!', 'success');
          // Fetch lại chi tiết ghi chú vừa tạo để lấy files mới nhất
          try {
            const detailRes = await fetch(require('../../api/endpoints').PERSONAL_NOTES_ENDPOINTS.UPDATE_NOTE(createdNoteId), {
              headers: { 'Authorization': `Bearer ${token}` }
            });
            if (detailRes.ok) {
              updatedNote = await detailRes.json();
            }
          } catch {}
        } else {
          showToast('Tải tệp thất bại!', 'error');
        }
      } catch {
        showToast('Có lỗi khi tải tệp!', 'error');
      }
    }
    setTitle('');
    setContent('');
    setAttachments([]);
    if (fileInputRef.current) fileInputRef.current.value = "";
    // Trả về dữ liệu ghi chú mới nhất cho component cha nếu có
    if (onCreate) onCreate(updatedNote);
    onClose && onClose();
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose && onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

    const fileInputRef = useRef(null);

  return (
    <div className="create-note-modal">
      <form onSubmit={handleSubmit} className="create-note-form" ref={modalRef}>
        <div className="create-note-header">
          <h2 className="create-note-title">Tạo ghi chú mới</h2>
          <button type="button" onClick={onClose} className="create-note-close">
            <img src={closeLoc} alt="Close" />
          </button>
        </div>
        <div className="create-note-input-container">
          <input
            type="text"
            placeholder="Tiêu đề"
            value={title}
            onChange={e => setTitle(e.target.value)}
            className={`create-note-input ${errors.title ? 'create-note-input-error' : ''}`}
          />
          {errors.title && <div className="create-note-error-message">{errors.title}</div>}
        </div>
        <div className="create-note-textarea-container">
          <textarea
            placeholder="Nội dung ghi chú"
            value={content}
            onChange={e => setContent(e.target.value)}
            rows={5}
            className={`create-note-textarea ${errors.content ? 'create-note-input-error' : ''}`}
          />
          {errors.content && <div className="create-note-error-message">{errors.content}</div>}
        </div>
        <div className="job-panel-rows">
            <div className="job-panel-label">Tệp đính kèm</div>
            <div className="job-panel-value" style={{flexDirection: 'column', alignItems: 'flex-start', gap: 0}}>
              <div className="job-panel-file-upload-custom" onClick={handleAddFile}>
                <input
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx"
                  onChange={handleFileUpload}
                  style={{ display: "none" }}
                  ref={fileInputRef}
                />
                <img src={loadFileIcon} alt="Tải tệp" className="job-panel-file-upload-icon" />
                <span className="job-panel-file-upload-text">Bấm vào để tải lên tệp</span>
              </div>
              {attachments.length > 0 && (
                <div className="job-panel-file-list" style={{width: '100%'}}>
                  {attachments.map((file, idx) => (
                    <div className="job-panel-file-item" key={idx}>
                      <img src={fileTextIcon} alt="file" className="job-panel-file-icon" />
                      <span className="job-panel-file-name">{file.name}</span>
                      <button type="button" className="job-panel-remove-file-btn" onClick={() => {
                        setAttachments(prev => prev.filter((_, i) => i !== idx));
                      }} title="Xóa tệp">×</button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        <div className="create-note-buttons">
          <button type="button" className="create-note-cancel" onClick={onClose}>
            Hủy
          </button>
          <button type="submit" className="create-note-submit">
            Tạo
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateNote;
