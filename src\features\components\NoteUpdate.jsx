import React, { useState, useEffect, useRef } from 'react';
import DocsIcon from '../../assets/docs.svg';
import PdfIcon from '../../assets/pdf.svg';
import ImageIcon from '../../assets/image.svg';
import MoreIcon from '../../assets/detail.svg';
import DownloadIcon from '../../assets/download.svg';
import DeleteIcon from '../../assets/trash.svg';
import loadFileIcon from "../../assets/loadfile.svg";
import fileTextIcon from "../../assets/file-text.svg";
import '../../styles/UpdateNote.css';
import closeLoc from '../../assets/closeLoc.svg';
import { validateNoteForm } from '../../utils/validation';
import { showToast } from '../../utils/toastUtils';

const UpdateNote = ({ note, onClose, onUpdate, onDelete }) => {
  const [showFileMenu, setShowFileMenu] = useState(null); // index of file menu open
  const [updatedNote, setUpdatedNote] = useState({
    title: '',
    content: ''
  });
  const [errors, setErrors] = useState({ title: '', content: '' });
  const modalRef = useRef(null);

  useEffect(() => {
    if (note) {
      setUpdatedNote({
        title: note.title,
        content: note.content
      });
      setShowFileMenu(null);
    }
  }, [note]);

  const validate = () => {
    const validationErrors = validateNoteForm({ 
      title: updatedNote.title, 
      content: updatedNote.content 
    });
    setErrors(validationErrors);
    return !validationErrors.title && !validationErrors.content;
  };

  const handleUpdate = () => {
    if (validate()) {
      onUpdate(updatedNote);
      onClose();
    }
  };

  const handleDelete = () => {
    onDelete(note.id);
    onClose();
  };

  const handleAddFile = () => {
    fileInputRef.current?.click();
  };

   const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...files],
    }));
    // Reset input để có thể chọn lại cùng file
    e.target.value = "";
  };

  const [formData, setFormData] = useState({
      name: "",
      description: "",
      startDate: "",
      endDate: "",
      priority: "medium",
      members: [],
      attachments: [],
    });

    const fileInputRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose && onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  return (
    <div className="update-note-modal">
      <form className="update-note-form" ref={modalRef} onSubmit={e => { e.preventDefault(); handleUpdate(); }}>
        <div className="update-note-header">
          <h2 className="update-note-title">Chỉnh sửa ghi chú</h2>
          <button type="button" className="update-note-close" onClick={onClose}>
            <img src={closeLoc} alt="Close" />
          </button>
        </div>
        <div className="update-note-input-container">
          <input
            type="text"
            value={updatedNote.title}
            onChange={e => setUpdatedNote({ ...updatedNote, title: e.target.value })}
            placeholder=""
            className={`update-note-input ${errors.title ? 'update-note-input-error' : ''}`}
          />
          {errors.title && <div className="update-note-error-message">{errors.title}</div>}
        </div>
        <div className="update-note-textarea-container">
          <textarea
            value={updatedNote.content}
            onChange={e => setUpdatedNote({ ...updatedNote, content: e.target.value })}
            placeholder=""
            rows={5}
            className={`update-note-textarea ${errors.content ? 'update-note-input-error' : ''}`}
          />
          {errors.content && <div className="update-note-error-message">{errors.content}</div>}
        {/* Hiển thị danh sách tệp đính kèm */}
        <div className="update-note-files">
          <div className="update-note-files-label">Tệp tài liệu</div>
          <div className="job-panel-rows">
                    <div className="job-panel-value" style={{flexDirection: 'column', alignItems: 'flex-start', gap: 0}}>
                      <div className="job-panel-file-upload-custom" onClick={handleAddFile}>
                        <input
                          type="file"
                          multiple
                          accept=".pdf,.doc,.docx"
                          onChange={handleFileUpload}
                          style={{ display: "none" }}
                          ref={fileInputRef}
                        />
                        <img src={loadFileIcon} alt="Tải tệp" className="job-panel-file-upload-icon" />
                        <span className="job-panel-file-upload-text">Bấm vào để tải lên tệp</span>
                      </div>
                      {formData.attachments && formData.attachments.length > 0 && (
                        <div className="job-panel-file-list" style={{width: '100%'}}>
                          {formData.attachments.map((file, idx) => (
                            <div className="job-panel-file-item" key={idx}>
                              <img src={fileTextIcon} alt="file" className="job-panel-file-icon" />
                              <span className="job-panel-file-name">{file.name}</span>
                              <button type="button" className="job-panel-remove-file-btn" onClick={() => {
                                setFormData(prev => ({
                                  ...prev,
                                  attachments: prev.attachments.filter((_, i) => i !== idx)
                                }));
                              }} title="Xóa tệp">×</button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
        </div>
        
          {note.attachments && note.attachments.length > 0 ? (
            note.attachments.map((file, idx) => {
              let icon = DocsIcon;
              const ext = (file.name || '').split('.').pop().toLowerCase();
              if (ext === 'pdf') icon = PdfIcon;
              else if (["jpg","jpeg","png","gif","bmp","svg"].includes(ext)) icon = ImageIcon;
              else if (["doc","docx","xls","xlsx","ppt","pptx"].includes(ext)) icon = DocsIcon;
              return (
                <div key={idx} className="update-note-file-row" style={{ display: 'flex', alignItems: 'center', background: '#f9eaea', borderRadius: 8, marginBottom: 8, padding: '6px 12px', position: 'relative' }}>
                  <img src={icon} alt={ext + '-icon'} style={{ width: 20, height: 20, marginRight: 8 }} />
                  <span style={{ fontSize: 15 }}>{file.name || 'Tệp đính kèm'}</span>
                  <button type="button" className="update-note-file-menu-btn" style={{ background: 'none', border: 'none', marginLeft: 'auto', cursor: 'pointer' }} onClick={() => setShowFileMenu(showFileMenu === idx ? null : idx)}>
                    <img src={MoreIcon} alt="menu" style={{ width: 22, height: 22 }} />
                  </button>
                  {showFileMenu === idx && (
                    <div className="update-note-file-menu" style={{ position: 'absolute', top: 36, right: 0, background: '#fff', boxShadow: '0 2px 8px rgba(0,0,0,0.12)', borderRadius: 8, zIndex: 10, minWidth: 140 }}>
                      <div style={{ padding: '8px 12px', fontWeight: 500, fontSize: 13, color: '#888' }}>Hành động</div>
                      <button type="button" style={{ display: 'flex', alignItems: 'center', width: '100%', background: 'none', border: 'none', padding: '8px 12px', cursor: 'pointer', fontSize: 14 }} onClick={() => { setShowFileMenu(null); window.open(file.url || '#', '_blank'); }}>
                        <img src={DownloadIcon} alt="download" style={{ width: 18, height: 18, marginRight: 8 }} /> Tải file
                      </button>
                      <button type="button" style={{ display: 'flex', alignItems: 'center', width: '100%', background: 'none', border: 'none', padding: '8px 12px', cursor: 'pointer', color: '#e53935', fontSize: 14 }} onClick={() => { setShowFileMenu(null); alert('Xoá file demo!'); }}>
                        <img src={DeleteIcon} alt="delete" style={{ width: 18, height: 18, marginRight: 8 }} /> Xoá file
                      </button>
                    </div>
                  )}
                </div>
              );
            })
          ) : (
            <div style={{ color: '#888', fontSize: 15, padding: '8px 0 8px 8px' }}>Không có tài liệu</div>
          )}
        </div>
        
        <div className="update-note-buttons">
          <button type="button" className="update-note-delete" onClick={handleDelete}>
            Xoá ghi chú
          </button>
          <button type="submit" className="update-note-submit">
            Cập nhật ghi chú
          </button>
        </div>
      </form>
    </div>
  );
};

export default UpdateNote;
